import React, { useState } from "react";
import { Dialog } from "primereact/dialog";
import { <PERSON><PERSON> } from "primereact/button";
import { InputTextarea } from "primereact/inputtextarea";
import { useSelector } from "react-redux";
import { API } from "../constants/api_url";
import APIServices from "../service/APIService";
import { AttachmentComponent } from "../components/Forms/Attachment";
import { Tooltip } from "primereact/tooltip";
import { FileUpload } from "primereact/fileupload";
import Swal from "sweetalert2";
import moment from "moment";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Calendar } from "primereact/calendar";
import { Dropdown } from "primereact/dropdown";
import { InputText } from "primereact/inputtext";
import useForceUpdate from "use-force-update";

const statusColors = {
    Completed: "#29C76F",
    "In Progress": "#F5C37B",
    "Not Started": "#CCCED5",
    Blocked: "#fff",
    "Rejected": 'orangered'
};

const QualitativeData = ({ data, refresh }) => {
    const login_data = useSelector((state) => state.user.userdetail);
    const [visible, setVisible] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [formData, setFormData] = useState({});
    const [editingValue, setEditingValue] = useState('');
    const forceUpdate = useForceUpdate()
    console.log(data)
    const handleFileUpload = async (files, fieldName) => {
        const allowedext = ['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.bmp', '.pdf', '.PDF', '.xls', '.xlsx', '.doc', '.docx', '.ppt', '.pptx'];

        const validFiles = files.filter(file => {
            const ext = file.name.substring(file.name.lastIndexOf('.'));
            return allowedext.includes(ext);
        });

        if (!validFiles.length) {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Invalid file format. Supported: JPEG, PNG, PDF, DOCX etc.",
                showConfirmButton: false,
                timer: 2000,
            });
            return;
        }

        const formDataToUpload = new FormData();
        validFiles.forEach(file => {
            formDataToUpload.append("file", file);
        });

        try {
            const res = await APIServices.post(API.FilesUpload, formDataToUpload, {
                headers: { "Content-Type": "multipart/form-data" }
            });

            const uploadedFile = res.data?.files?.[0]; // assuming your backend returns array of files
            if (uploadedFile?.originalname) {
                // Construct the URL manually
                const url = `${API.baseurl}docs/${uploadedFile.originalname}`;
                setFormData(prev => ({
                    ...prev,
                    [fieldName]: url
                }));
            }

        } catch (err) {
            console.error("File upload failed", err);
            Swal.fire({
                position: "center",
                icon: "error",
                title: "File upload failed",
                showConfirmButton: false,
                timer: 2000,
            });
        }
    };

    const addRow = (data, field,index) => {
        const current = formData[field.name] || [];
        const updated = [...current, data[0]];
        setFormData({ ...formData, [field.name]: updated });
        forceUpdate();
    }
    const deleteRow = (rowindex, field) => {
        const current = formData[field.name] || [];
        const updated = current.filter((_, index) => index !== rowindex);
        setFormData({ ...formData, [field.name]: updated });
        forceUpdate();
    }
    const actionTemplate = (rowData, rowindex, field) => {
        const current = formData[field.name] || [];

        return (
            <>
                <Button
                    icon="pi pi-trash"
                    className="mr-2 actionbtn" style={{
                        width: '20px',
                        height: '20px',
                        background: 'transparent',
                        color: 'palevioletred'
                    }}
                    onClick={() => {

                        deleteRow(rowindex, field)

                    }}
                />
            </>
        )
    }
    const renderTableData = (rowData) => {
        console.log('=== RENDERING TABLE DATA ===');
        console.log('Row Data:', rowData);

        try {
            if (!rowData || !rowData.data) {
                console.log('No rowData or rowData.data');
                return <div>No data</div>;
            }

            const isError = rowData.data.error === 1;
            const cellStyle = {
                color: isError ? 'red' : 'inherit',
                cursor: rowData.type === 5 ? 'default' : 'pointer',
                padding: '8px',
                minHeight: '30px',
                display: 'flex',
                alignItems: 'center',
                width: '100%'
            };

            // Get the current value, ensuring we handle different data types
            const currentValue = rowData.data.value;
            console.log('Current value for display:', currentValue, 'Type:', typeof currentValue);
            console.log('Full rowData.data:', rowData.data);

            // For type 5 (labels), don't allow editing
            if (rowData.type === 5) {
                return (
                    <div style={cellStyle}>
                        {rowData.data.label || 'Label'}
                    </div>
                );
            }

            // For all other types, allow cell editing by not preventing default behavior
            switch (rowData.type) {
                case 1:
                case 2:
                case 3:
                    const displayValue = (currentValue === undefined || currentValue === null || currentValue === '')
                        ? 'click here'
                        : String(currentValue);
                    return (
                        <div style={cellStyle}>
                            {displayValue}
                        </div>
                    );
                case 4:
                    console.log('=== DROPDOWN DISPLAY DEBUG ===');
                    console.log('currentValue:', currentValue, 'Type:', typeof currentValue);
                    console.log('rowData.data.values:', rowData.data.values);

                    const selectedOption = rowData.data.values && currentValue !== null && currentValue !== undefined
                        ? rowData.data.values.find((i) => i.value === currentValue)
                        : null;

                    console.log('selectedOption found:', selectedOption);
                    console.log('Will display:', selectedOption ? selectedOption.label : 'Select option');

                    return (
                        <div style={cellStyle}>
                            {selectedOption ? selectedOption.label : 'Select option'}
                        </div>
                    );
                case 6:
                    const dateDisplay = (currentValue === undefined || currentValue === null)
                        ? 'click here'
                        : moment(currentValue).format('DD-MM-YYYY');
                    return (
                        <div style={cellStyle}>
                            {dateDisplay}
                        </div>
                    );
                default:
                    const defaultDisplay = (currentValue === undefined || currentValue === null || currentValue === '')
                        ? 'click here'
                        : String(currentValue);
                    return (
                        <div style={cellStyle}>
                            {defaultDisplay}
                        </div>
                    );
            }
        } catch (error) {
            console.error('Error in renderTableData:', error);
            return (
                <div>Error rendering data</div>
            );
        }
    }
    // Add the getObjectAtIndex function from reference code
    const getObjectAtIndex = (data, index) => {
        const keys = Object.keys(data);
        const key = keys[index];
        return data[key];
    };

    const onCellEditComplete = (e, field) => {
        try {
            let { rowData, newValue, cellIndex, field: columnField, rowIndex, headerName } = e;
            console.log('=== CELL EDIT COMPLETE ===');
            console.log('Event:', e);
            console.log('Field:', field);
            console.log('Row Data:', rowData);
            console.log('Column Field:', columnField);
            console.log('Row Index:', rowIndex);
            console.log('Cell Index:', cellIndex);
            console.log('New value:', newValue);
            console.log('Row data column field value:', rowData[columnField]);
            console.log('Current formData structure:', formData);
            console.log('Field headers:', field.headers);
            console.log('Current row data:', formData[field.name] ? formData[field.name][rowIndex] : 'No data');

            // The value should be in rowData[columnField] - let's check what we actually have
            console.log('Available keys in rowData:', Object.keys(rowData));
            console.log('columnField value:', columnField);
            console.log('Value from rowData[columnField]:', rowData[columnField]);
            console.log('Complete rowData structure:', rowData);

            // Get the actual value - handle different scenarios
            let actualValue;

            console.log('=== CELL EDIT COMPLETE DEBUG ===');
            console.log('newValue:', newValue);
            console.log('rowData[columnField]:', rowData[columnField]);
            console.log('headerName:', headerName);

            if (newValue !== undefined) {
                // User typed something new
                actualValue = newValue;
                console.log('Using newValue:', actualValue);
            } else if (headerName && rowData[headerName] && rowData[headerName].data) {
                // No new value, preserve existing value from headerName
                actualValue = rowData[headerName].data.value;
                console.log('Preserving existing value from headerName:', actualValue);
            } else if (rowData[columnField]) {
                // Fallback to columnField
                actualValue = rowData[columnField];
                console.log('Using rowData[columnField]:', actualValue);
            } else {
                // Last resort - try to find the value in formData
                const currentFormData = formData[field.name];
                if (currentFormData && currentFormData[rowIndex] && headerName && currentFormData[rowIndex][headerName]) {
                    actualValue = currentFormData[rowIndex][headerName].data?.value;
                    console.log('Using value from current formData:', actualValue);
                }
            }

            console.log('Final actualValue to save:', actualValue);

            // Follow the same pattern as reference code
            let loc = JSON.parse(JSON.stringify(formData));
            setTimeout(() => {
                try {
                    console.log('Before update - loc structure:', loc[field.name][rowIndex]);
                    console.log('Updating with headerName:', headerName, 'cellIndex:', cellIndex, 'actualValue:', actualValue);

                    // Use headerName if available, otherwise fall back to cellIndex approach
                    if (headerName && loc[field.name][rowIndex][headerName]) {
                        // Update using header name directly
                        console.log('Before update - cell data:', loc[field.name][rowIndex][headerName]);
                        if (!loc[field.name][rowIndex][headerName].data) {
                            loc[field.name][rowIndex][headerName].data = {};
                        }
                        loc[field.name][rowIndex][headerName].data['value'] = actualValue;
                        console.log('Updated via header name:', headerName, 'with value:', actualValue);
                        console.log('After update - cell data:', loc[field.name][rowIndex][headerName]);
                    } else {
                        // Fallback to cellIndex approach
                        let item = getObjectAtIndex(loc[field.name][rowIndex], cellIndex);
                        console.log('Item found at cellIndex:', item);

                        // Ensure the data object exists
                        if (!item.data) {
                            item.data = {};
                        }

                        item.data['value'] = actualValue;
                        console.log('Updated item.data.value to:', actualValue);

                        // Also try to update using header name as backup
                        const headerNameFromIndex = field.headers[cellIndex];
                        console.log('Header name for cellIndex:', headerNameFromIndex);

                        if (loc[field.name][rowIndex][headerNameFromIndex]) {
                            if (!loc[field.name][rowIndex][headerNameFromIndex].data) {
                                loc[field.name][rowIndex][headerNameFromIndex].data = {};
                            }
                            loc[field.name][rowIndex][headerNameFromIndex].data['value'] = actualValue;
                            console.log('Updated via header name as well');
                        }
                    }

                    // Clean up any temporary fields
                    if (columnField && columnField.startsWith('field_')) {
                        delete loc[field.name][rowIndex][columnField];
                    }

                    console.log('After update - loc structure:', loc[field.name][rowIndex]);

                    setFormData(loc);
                    console.log('Updated formData:', loc);
                    forceUpdate();
                } catch (updateError) {
                    console.error('Error updating cell data:', updateError);
                    console.error('Error details:', updateError.stack);
                }
            }, 100);

        } catch (error) {
            console.error('Error in onCellEditComplete:', error);
        }
    };
    const renderEditor = (options) => {
        try {
            // Get item by cellIndex from field or use headerName if available
            let item;
            if (options.headerName && options.rowData[options.headerName]) {
                item = options.rowData[options.headerName];
            } else {
                item = getObjectAtIndex(options.rowData, parseInt(options.field.split('_')[1]));
            }

            console.log('=== RENDER EDITOR DEBUG ===');
            console.log('Editor for field:', options.field, 'headerName:', options.headerName);
            console.log('Item:', item);
            console.log('Options.value:', options.value, 'Type:', typeof options.value);
            console.log('Item.data:', item?.data);
            console.log('Item.data.value:', item?.data?.value);

            // Extract the actual value from the data structure
            let initialValue = '';

            // First check if the value was passed directly in options (from our modified editor call)
            if (options.value !== undefined && options.value !== null && typeof options.value !== 'object') {
                initialValue = options.value;
                console.log('Using options.value:', initialValue);
            } else if (item && item.data && item.data.value !== undefined && item.data.value !== null) {
                initialValue = item.data.value;
                console.log('Using item.data.value:', initialValue);
            }

            // Convert initialValue to string for display, handle different types
            let displayValue = '';
            if (initialValue !== null && initialValue !== undefined) {
                if (typeof initialValue === 'object') {
                    displayValue = '';  // Don't show objects
                    console.log('Detected object, using empty string');
                } else {
                    displayValue = String(initialValue);
                    console.log('Using string value:', displayValue);
                }
            }

            console.log('Final displayValue for editor:', displayValue);

            // Use defaultValue for uncontrolled components (like SectionBox.js)
            switch (item.type) {
                case 1:
                    return (<InputText
                        type="text"
                        defaultValue={displayValue}
                        onChange={(e) => {
                            console.log('InputText onChange:', e.target.value);
                            options.editorCallback(e.target.value);
                        }}
                        autoFocus
                        style={{ width: '100%' }}
                    />);
                case 2:
                    return (<InputTextarea
                        defaultValue={displayValue}
                        onChange={(e) => {
                            console.log('InputTextarea onChange:', e.target.value);
                            options.editorCallback(e.target.value);
                        }}
                        autoFocus
                        style={{ width: '100%' }}
                    />);
                case 3:
                    return (<InputText
                        type="number"
                        defaultValue={displayValue}
                        onChange={(e) => {
                            console.log('InputNumber onChange:', e.target.value);
                            options.editorCallback(e.target.value);
                        }}
                        autoFocus
                        style={{ width: '100%' }}
                    />);
                case 4:
                    return (<Dropdown
                        optionLabel="label"
                        optionValue="value"
                        value={initialValue}
                        options={item.data?.values || []}
                        onChange={(e) => {
                            console.log('Dropdown onChange:', e.value);
                            options.editorCallback(e.value);
                        }}
                        autoFocus
                    />);
                case 6:
                    return (<Calendar
                        dateFormat="dd/mm/yy"
                        value={initialValue}
                        onChange={(e) => {
                            console.log('Calendar onChange:', e.value);
                            options.editorCallback(e.value);
                        }}
                    />);
                case 5:
                    return null;
                default:
                    return (<InputText
                        type="text"
                        defaultValue={displayValue}
                        onChange={(e) => {
                            console.log('Default InputText onChange:', e.target.value);
                            options.editorCallback(e.target.value);
                        }}
                        autoFocus
                        style={{ width: '100%' }}
                    />);
            }


        } catch (error) {
            console.error('Error in renderEditor:', error);
            return (
                <InputText
                    type="text"
                    value=""
                    onChange={(e) => {
                        try {
                            options.editorCallback(e.target.value);
                        } catch (err) {
                            console.error('Error in fallback editor callback:', err);
                        }
                    }}
                    autoFocus
                    style={{ width: '100%' }}
                />
            );
        }
    }
    const openDialog = (item) => {
        try {
            if (!item) {
                console.error('No item provided to openDialog');
                return;
            }

            setSelectedItem(item);

            const prefilled = {};
            if (item.form && Array.isArray(item.form)) {
                item.form.forEach((field) => {
                    try {
                        if (["radio-group", "textarea", "text", "file"].includes(field.type)) {
                            prefilled[field.name] = field.value || "";
                        } else if (field.type === "checkbox-group") {
                            prefilled[field.name] = field.values?.filter(v => v.selected)?.map(v => v.value) || [];
                        } else if (field.type === "tableadd") {
                            prefilled[field.name] = field.value || [];
                        }
                    } catch (fieldError) {
                        console.error('Error processing field:', field, fieldError);
                    }
                });
            }

            console.log('Prefilled data:', prefilled);
            console.log('Selected item:', item);

            setFormData(prefilled);
            setVisible(true);
        } catch (error) {
            console.error('Error in openDialog:', error);
        }
    };

    const handleSave = async (status) => {
        console.log(selectedItem)
        if (!selectedItem?.id) return;

        const requiredKeys = selectedItem?.form?.length ? selectedItem?.form?.filter(x => x.required).map(x => x.name) : null
        let hasValidData = requiredKeys ? requiredKeys.every(key => {
            const value = formData[key];
            return (typeof value === 'string' && value.trim() !== '') ||
                (Array.isArray(value) && value.length > 0);
        }) : false

        // Additional validation for tableadd fields when status is "Completed"
        if (status === "Completed" && selectedItem?.form) {
            let total = 0;
            let count = 0;

            selectedItem.form.forEach(item => {
                if (item.type === 'tableadd' && item.required && (!formData[item.name] || formData[item.name].length === 0)) {
                    total -= total
                    item.error = 1
                    hasValidData = false
                } else {
                    delete item.error
                }

                // Validate nested fields in tableadd data
                if (item.type === 'tableadd' && formData[item.name]) {
                    Object.values(formData[item.name]).forEach((i) => {
                        Object.values(i).forEach((j) => {
                            if (j.type === 1 && j.data.required) {
                                total += 1
                                if (j.data.value !== undefined && j.data.value.trim().length !== 0) {
                                    count += 1
                                    delete j.data.error
                                } else {
                                    j.data.error = 1
                                    hasValidData = false
                                }
                            } else if (j.type === 2 && j.data.required) {
                                total += 1
                                if (j.data.value !== undefined && j.data.value.trim().length !== 0) {
                                    count += 1
                                    delete j.data.error
                                } else {
                                    j.data.error = 1
                                    hasValidData = false
                                }
                            } else if (j.type === 3 && j.data.required) {
                                total += 1
                                if (j.data.value !== undefined && !isNaN(j.data.value) && j.data.value.toString().trim().length !== 0) {
                                    count += 1
                                    delete j.data.error
                                } else {
                                    j.data.error = 1
                                    hasValidData = false
                                }
                            } else if (j.type === 4 && j.data.required) {
                                total += 1
                                if (j.data.value !== undefined && j.data.value !== null) {
                                    count += 1
                                    delete j.data.error
                                } else {
                                    j.data.error = 1
                                    hasValidData = false
                                }
                            } else if (j.type === 6 && j.data.required) {
                                total += 1
                                if (j.data.value !== undefined && j.data.value !== null && (typeof j.data.value === 'object' || typeof j.data.value === 'string')) {
                                    count += 1
                                    delete j.data.error
                                } else {
                                    j.data.error = 1
                                    hasValidData = false
                                }
                            }
                        })
                    })
                }
            });
        }

        if (hasValidData || status === "In Progress") {
            const payload = {
                [login_data.id]: { ...formData, status }
            };
            try {
                const response = await APIServices.post(API.SaveQualitativeResponse(selectedItem.id), payload);
                if (response.data.status) {
                    setVisible(false);
                    refresh()
                } else {
                    console.error("Save failed", response);
                }
            } catch (err) {
                console.error("Error saving response", err);
            }
        } else {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Request to fill all mandatory question to complete submission",
                showConfirmButton: false,
                timer: 2000,
            });
        }

    };

    const frameworkMap = {
        2: "GRI",
        3: "ISSB",
        4: "MCfS",
        5: "SASB",
        6: "TCFD",
        7: "BRSR",
        8: "SGX",
        9: "Boursa Kuwait",
        10: "Bursa Malaysia",
        11: "HKEX",
        12: "NASDAQ",
        13: "CDP",
        14: "EcoVadis",
        15: "CDP",
        16: "EcoVadis",
        17: "MSCI",
        18: "S&P Dow Jones",
        19: "Sustainalitics",
        20: "ISS",
        21: "Carbon Footprint",
        22: "GEF capital",
        24: "BRSR Core",
        25: "CSRD",
        26: "DJSI",
        27: "ESRS",
        28: "IFRS S1",
        29: "IFRS S2",
    };

    return (
        <>
            <style>
                {`
                    .p-datatable .p-datatable-tbody > tr > td .p-inputtext,
                    .p-datatable .p-datatable-tbody > tr > td .p-inputtextarea,
                    .p-datatable .p-datatable-tbody > tr > td .p-dropdown,
                    .p-datatable .p-datatable-tbody > tr > td .p-calendar {
                        width: 100% !important;
                        min-width: 120px !important;
                        z-index: 1000 !important;
                        position: relative !important;
                        background: white !important;
                        border: 1px solid #ced4da !important;
                    }

                    .p-datatable .p-datatable-tbody > tr > td.p-cell-editing {
                        padding: 2px !important;
                        background: #f8f9fa !important;
                    }

                    .p-datatable .p-datatable-tbody > tr > td.p-cell-editing > * {
                        width: 100% !important;
                    }

                    .p-dropdown-panel,
                    .p-calendar-panel {
                        z-index: 9999 !important;
                    }

                    .p-inputtextarea {
                        resize: vertical !important;
                        min-height: 60px !important;
                    }

                    /* Make editable cells clearly clickable */
                    .p-datatable .p-datatable-tbody > tr > td.p-editable-column {
                        cursor: pointer !important;
                    }

                    .p-datatable .p-datatable-tbody > tr > td.p-editable-column:hover {
                        background-color: #f8f9fa !important;
                    }

                    /* Disabled cells should not be clickable */
                    .p-datatable .p-datatable-tbody > tr > td.p-disabled {
                        cursor: default !important;
                        background-color: #f5f5f5 !important;
                        color: #6c757d !important;
                    }

                    /* Ensure cell content is properly displayed */
                    .p-datatable .p-datatable-tbody > tr > td > div {
                        min-height: 30px;
                        display: flex;
                        align-items: center;
                        width: 100%;
                    }
                `}
            </style>
            <div className="p-4 border rounded-lg shadow-sm bg-white mb-4">
                <h2 className="fs-4 fw-bold mb-4">{data[0]?.title || "Environment"}</h2>

                <div style={{ overflowX: "auto", maxWidth: "100%" }}>
                    <table className="table table-bordered" style={{ minWidth: "1000px", width: "100%" }}>
                        <tbody>
                            {data.map((env, index) => (
                                <tr key={index}>
                                    <td
                                        className="p-4 align-top bg-white"
                                        style={{
                                            width: "300px",
                                            minWidth: "250px",
                                            position: "sticky",
                                            left: 0,
                                            zIndex: 2,
                                        }}
                                    >
                                        <h3 className="fw-semibold fs-5">{env.subHeading}</h3>
                                        <p className="text-muted"> <strong>Reporting Entity:</strong> {env.location}</p>
                                    </td>

                                    <td className="p-0">
                                        <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                            {env.data.map((item, i) => {
                                                const isBlocked = item.status === "Blocked";
                                                return (
                                                    <div
                                                        key={i}
                                                        className={`d-flex flex-column border flex-shrink-0 ${isBlocked ? "text-muted" : "hover-effect"}`}
                                                        style={{
                                                            width: "250px",
                                                            minWidth: "250px",
                                                            cursor: isBlocked ? "not-allowed" : "pointer",
                                                            opacity: isBlocked ? 0.8 : 1,
                                                            pointerEvents: isBlocked ? "none" : "auto",
                                                            borderColor: isBlocked ? "transparent" : undefined,
                                                        }}
                                                        onClick={() => !isBlocked && openDialog(item)}
                                                    >
                                                        <div
                                                            className="w-100"
                                                            style={{ height: "10px", backgroundColor: item?.rejected ? statusColors.Rejected : statusColors[item.status] || "#fff" }}
                                                        />
                                                        <div className="p-3 flex-grow-1 flex justify-content-between flex-column">
                                                            <h5 className="fw-bold text-wrap">{item.name}</h5>
                                                            {!isBlocked && <p className={"text-muted mt-2"}>Due date:<br /> <span style={{ fontWeight: item.isOverdue ? 'bold' : 'unset', color: item.isOverdue ? 'rgba(var(--bs-danger-rgb),var(--bs-bg-opacity))!important' : 'unset' }}>{item.dueDate}</span> </p>}
                                                        </div>
                                                        {item.isLocked && (
                                                            <div className="p-2 d-flex justify-content-end">
                                                                <i className="pi pi-lock text-secondary"></i>
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Dialog */}
                <Dialog
                    header={selectedItem?.name}
                    visible={visible}
                    style={{ width: "50vw" }}
                    onHide={() => {
                        try {
                            setVisible(false);
                            setSelectedItem(null);
                            setFormData({});
                        } catch (error) {
                            console.error('Error closing dialog:', error);
                            setVisible(false);
                        }
                    }}
                    modal={true}
                    dismissableMask={false}
                    closeOnEscape={true}
                >
                    <div className="p-3" onClick={(e) => e.stopPropagation()}>
                        <h6 className="fw-bold">Instructions for entering data:</h6>
                        <p className="text-muted"> Enter your response in the provided text box. Be as clear, specific, and accurate as possible</p>
                        <ul>
                            <li>If supporting documents are required, use the Upload button to attach files.</li>
                            <li>Save your progress regularly by clicking “Save Progress”</li>
                            <li>Responses from multiple entities will be combined for Group-level reporting. When you see the word 'Group level,' tailor your response to the specific reporting entity you are addressing in these qualitative questions.</li>
                        </ul>
                        {selectedItem?.rejected === 1 && <div className="col-12 grid m-0 p-1  fw-7 mb-3 ">
                            <label className="fw-bold  col-6">Remarks <span className="mandatory" >{selectedItem?.rejection_remarks}</span> </label>
                            <label className='col-6'> Rejected on : {selectedItem?.rejected_on ? moment(selectedItem?.rejected_on).format('DD-MMM-YYYY') : ''}  </label>


                        </div>}
                        {Array.isArray(selectedItem?.form) && selectedItem.form.map((field, index) => (


                            <div className="mt-3" key={index}>
                                <Tooltip target={`.tooltip${index}`} position="top" />
                                {field.assignedFramework && (
                                    <div className="d-flex flex-wrap gap-2 mt-2">
                                        {Object.entries(field.assignedFramework).map(([frameworkId, labels]) =>
                                            labels.map((labelVal, i) => (
                                                <span
                                                    key={`${frameworkId}-${i}`}
                                                    className="badge bg-primary text-white px-2 py-1 rounded"
                                                    style={{ fontSize: "0.85rem" }}
                                                >
                                                    {frameworkMap[frameworkId]} - {labelVal}
                                                </span>
                                            ))
                                        )}
                                    </div>
                                )}
                                {(field.type === "textarea" || field.type === "text") && (
                                    <>
                                        <label className="fw-bold">

                                            {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                            {field.description && (
                                                <span>
                                                    <i
                                                        className={`material-icons fs-14 tooltip${index}`}
                                                        data-pr-tooltip={field.description}
                                                        style={{ fontSize: "14px", marginLeft: "4px" }}
                                                    >help</i>
                                                </span>
                                            )}
                                        </label>
                                        {field.type === "textarea" ? (
                                            <InputTextarea
                                                className="w-100 mt-2"
                                                value={formData[field.name] || ""}
                                                onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                            />
                                        ) : (
                                            <input
                                                type="text"
                                                className="form-control mt-2"
                                                value={formData[field.name] || ""}
                                                onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                            />
                                        )}
                                    </>
                                )}

                                {field.type === "radio-group" && (
                                    <>
                                        <label className="fw-bold">
                                            {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                            {field.description && (
                                                <span>
                                                    <i
                                                        className={`material-icons fs-14 tooltip${index}`}
                                                        data-pr-tooltip={field.description}
                                                        style={{ fontSize: "14px", marginLeft: "4px" }}
                                                    >help</i>
                                                </span>
                                            )}
                                        </label>
                                        <div className="mt-2">
                                            {field.values.map((option, i) => (
                                                <div className="form-check" key={i}>
                                                    <input
                                                        className="form-check-input"
                                                        type="radio"
                                                        name={field.name}
                                                        id={`${field.name}_${option.value}`}
                                                        value={option.value}
                                                        checked={formData[field.name] === option.value}
                                                        onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                                    />
                                                    <label className="form-check-label" htmlFor={`${field.name}_${option.value}`}>
                                                        {option.label}
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </>
                                )}

                                {field.type === "checkbox-group" && (
                                    <>
                                        <label className="fw-bold">
                                            {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                            {field.description && (
                                                <span>
                                                    <i
                                                        className={`material-icons fs-14 tooltip${index}`}
                                                        data-pr-tooltip={field.description}
                                                        style={{ fontSize: "14px", marginLeft: "4px" }}
                                                    >help</i>
                                                </span>
                                            )}
                                        </label>
                                        <div className="mt-2">
                                            {field.values.map((option, i) => (
                                                <div className="form-check" key={i}>
                                                    <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        id={`${field.name}_${option.value}`}
                                                        checked={formData[field.name]?.includes(option.value)}
                                                        onChange={(e) => {
                                                            const current = formData[field.name] || [];
                                                            const updated = e.target.checked
                                                                ? [...current, option.value]
                                                                : current.filter(v => v !== option.value);
                                                            setFormData({ ...formData, [field.name]: updated });
                                                        }}
                                                    />
                                                    <label className="form-check-label" htmlFor={`${field.name}_${option.value}`}>
                                                        {option.label}
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </>
                                )}

                                {field.type === "file" && (
                                    <>
                                        <label className="fw-bold">
                                            {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                            {field.description && (
                                                <span>
                                                    <i
                                                        className={`material-icons fs-14 tooltip${index}`}
                                                        data-pr-tooltip={field.description}
                                                        style={{ fontSize: "14px", marginLeft: "4px" }}
                                                    >help</i>
                                                </span>
                                            )}
                                        </label>


                                        <FileUpload
                                            name="file"
                                            customUpload
                                            uploadHandler={(e) => handleFileUpload(e.files, field.name)}
                                            accept=".jpg,.jpeg,.png,.bmp,.pdf,.xls,.xlsx,.doc,.docx,.ppt,.pptx"
                                            maxFileSize={20000000}
                                            chooseLabel="Upload Documents"
                                            uploadLabel="Upload"
                                            cancelLabel="Clear"
                                            mode="basic"
                                            auto
                                            className="mt-2"
                                        />

                                        {formData[field.name] && (
                                            <div className="mt-2">
                                                <a href={formData[field.name]} target="_blank" rel="noopener noreferrer">
                                                    {formData[field.name]}
                                                </a>
                                            </div>
                                        )}
                                    </>
                                )}
                                {field.type === "tableadd" && (
                                    <div className="flex flex-wrap  gap-3 p-card" style={{ marginBottom: 15, padding: 10 }}>
                                        <Tooltip target={".tooltip" + index} position='top' />
                                        <label style={{ display: 'flex' }} className='col-12'>{field.label.replace(/(<([^>]+)>)/gi, "")
                                            .replace(/\n/g, " ")
                                            .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {field.description !== undefined && field.description.trim().length !== 0 && <span><i style={{
                                                fontSize: '18px',
                                                marginLeft: '5px'
                                            }} className={`material-icons tooltip` + index} data-pr-tooltip={field.description}


                                            > help</i></span>} </label>
                                        <div className="flex justify-content-end" style={{ margin: 10, width: '100%' }}>
                                            {(formData[field.name] || []).length < field.maxrowlimit && <Button onClick={() => { addRow(field.newrow, field) }} icon='pi pi-plus'></Button>}
                                        </div>
                                        <DataTable
                                            scrollable
                                            showGridlines
                                            className="fullheight"
                                            style={{ width: '100%', maxHeight: 300 }}
                                            value={formData[field.name] || []}
                                            editMode="cell"
                                            onCellEditInit={(e) => {
                                                console.log('Cell edit init:', e);
                                                try {
                                                    // Ensure the cell is properly initialized for editing
                                                    const { rowIndex, field: columnField } = e;
                                                    console.log('Initializing edit for row:', rowIndex, 'field:', columnField);
                                                } catch (error) {
                                                    console.error('Error in onCellEditInit:', error);
                                                }
                                            }}
                                            onCellEditCancel={(e) => {
                                                console.log('Cell edit cancel:', e);
                                                try {
                                                    // Handle cancel gracefully
                                                    const { rowIndex, field: columnField } = e;
                                                    console.log('Cancelled edit for row:', rowIndex, 'field:', columnField);
                                                } catch (error) {
                                                    console.error('Error in onCellEditCancel:', error);
                                                }
                                            }}
                                        >
                                            {field.headers && field.headers.map((h, index) => {
                                                return <Column
                                                    key={`${field.name}-${h}`}
                                                    bodyClassName={(rowData) => {
                                                        return rowData[h] && rowData[h].type === 5 ? 'p-disabled' : ''
                                                    }}
                                                    header={h}
                                                    body={(rowData) => { return renderTableData(rowData[h]); }}
                                                    editor={(options) => {
                                                        // Extract the actual value from the cell data structure
                                                        const cellData = options.rowData[h];
                                                        console.log('Editor - cellData for header', h, ':', cellData);
                                                        console.log('Editor - options.value:', options.value);
                                                        console.log('Editor - full rowData:', options.rowData);

                                                        // Try to get the most up-to-date value from formData first
                                                        let actualValue = '';
                                                        const currentFormData = formData[field.name];
                                                        const rowIndex = options.rowIndex;

                                                        if (currentFormData && currentFormData[rowIndex] && currentFormData[rowIndex][h] && currentFormData[rowIndex][h].data) {
                                                            actualValue = currentFormData[rowIndex][h].data.value || '';
                                                            console.log('Editor - using updated formData value:', actualValue);
                                                        } else if (cellData?.data?.value !== undefined) {
                                                            actualValue = cellData.data.value || '';
                                                            console.log('Editor - using cellData value:', actualValue);
                                                        }

                                                        console.log('Editor - final actualValue:', actualValue);

                                                        const modifiedOptions = {
                                                            ...options,
                                                            field: `field_${index}`,
                                                            headerName: h,
                                                            value: actualValue,
                                                            cellIndex: index,
                                                            rowData: options.rowData
                                                        };
                                                        return renderEditor(modifiedOptions);
                                                    }}
                                                    onCellEditComplete={(e) => {
                                                        // Create a custom event with the header field
                                                        const modifiedEvent = {
                                                            ...e,
                                                            field: `field_${index}`,
                                                            headerName: h,
                                                            cellIndex: index,
                                                            columnField: h
                                                        };
                                                        onCellEditComplete(modifiedEvent, field);
                                                    }}
                                                />;
                                            })}
                                            <Column header='Action' style={{ width: 50 }} body={(rowData, e) => { return actionTemplate(rowData, e.rowIndex, field) }} />
                                        </DataTable>

                                    </div>
                                )
                                }
                            </div>
                        ))}
                    </div>

                    <div className="d-flex justify-content-start gap-2 mt-4">
                        <Button
                            label="Mark as Completed"
                            className="p-button-sm p-button-primary"
                            style={{ backgroundColor: "#004C77", borderColor: "#004C77" }}
                            onClick={() => handleSave("Completed")}
                        />
                        <Button
                            label="Save Progress"
                            className="p-button-sm p-button-outlined p-button-primary"
                            style={{ color: "#004C77", borderColor: "#004C77" }}
                            onClick={() => handleSave("In Progress")}
                        />
                        <Button
                            label="Cancel"
                            className="p-button-sm p-button-text"
                            style={{ color: "#004C77" }}
                            onClick={() => setVisible(false)}
                        />
                    </div>
                </Dialog>
            </div>
        </>
    );
};

export default QualitativeData;
